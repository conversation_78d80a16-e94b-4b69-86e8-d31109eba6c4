import 'package:flutter/material.dart';
import '../models/verse_model.dart';

class VerseCard extends StatelessWidget {
  final Verse verse;
  final String surahName;
  final bool isPlaying;
  final bool isFavorite;
  final VoidCallback onPlayTap;
  final VoidCallback onFavoriteTap;

  const VerseCard({
    super.key,
    required this.verse,
    required this.surahName,
    required this.isPlaying,
    required this.isFavorite,
    required this.onPlayTap,
    required this.onFavoriteTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              Colors.green.shade50,
              Colors.white,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس الآية
            Row(
              children: [
                // رقم الآية
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.green.shade700,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(
                    child: Text(
                      verse.number.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // معلومات الآية
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'آية ${verse.number}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      Text(
                        surahName,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // أزرار التحكم
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // زر التشغيل
                    IconButton(
                      onPressed: onPlayTap,
                      icon: Icon(
                        isPlaying ? Icons.pause_circle : Icons.play_circle,
                        color: Colors.green.shade700,
                        size: 32,
                      ),
                      splashRadius: 20,
                    ),
                    
                    // زر المفضلة
                    IconButton(
                      onPressed: onFavoriteTap,
                      icon: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: isFavorite ? Colors.red : Colors.grey.shade400,
                        size: 24,
                      ),
                      splashRadius: 20,
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // نص الآية
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.green.shade100,
                  width: 1,
                ),
              ),
              child: Text(
                verse.text,
                style: const TextStyle(
                  fontSize: 20,
                  height: 1.8,
                  color: Colors.black87,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.justify,
                textDirection: TextDirection.rtl,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // معلومات إضافية
            Row(
              children: [
                _buildInfoChip('الجزء ${verse.juz}', Colors.blue),
                const SizedBox(width: 8),
                _buildInfoChip('الصفحة ${verse.page}', Colors.orange),
                if (verse.sajda) ...[
                  const SizedBox(width: 8),
                  _buildInfoChip('سجدة', Colors.purple),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }
}
