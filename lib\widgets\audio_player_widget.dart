import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/audio_provider.dart';

class AudioPlayerWidget extends StatelessWidget {
  const AudioPlayerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AudioProvider>(
      builder: (context, audioProvider, child) {
        // إخفاء المشغل إذا لم يكن هناك تشغيل
        if (!audioProvider.isPlaying && 
            audioProvider.currentSurahNumber == null) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.shade700,
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: Colors.green.shade200,
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              // معلومات التشغيل الحالي
              Row(
                children: [
                  Icon(
                    audioProvider.currentVerseNumber != null
                        ? Icons.format_quote
                        : Icons.book,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          audioProvider.currentVerseNumber != null
                              ? 'آية ${audioProvider.currentVerseNumber}'
                              : 'السورة كاملة',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'سورة رقم ${audioProvider.currentSurahNumber}',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    audioProvider.selectedReciter?.arabicName ?? '',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // شريط التقدم
              Column(
                children: [
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: Colors.white,
                      inactiveTrackColor: Colors.white30,
                      thumbColor: Colors.white,
                      overlayColor: Colors.white24,
                      trackHeight: 3,
                      thumbShape: const RoundSliderThumbShape(
                        enabledThumbRadius: 6,
                      ),
                    ),
                    child: Slider(
                      value: audioProvider.totalDuration.inSeconds > 0
                          ? audioProvider.currentPosition.inSeconds.toDouble()
                          : 0.0,
                      max: audioProvider.totalDuration.inSeconds.toDouble(),
                      onChanged: (value) {
                        // يمكن إضافة وظيفة البحث في المستقبل
                      },
                    ),
                  ),
                  
                  // أوقات التشغيل
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _formatDuration(audioProvider.currentPosition),
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          _formatDuration(audioProvider.totalDuration),
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // أزرار التحكم
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // زر الإيقاف
                  IconButton(
                    onPressed: audioProvider.stop,
                    icon: const Icon(
                      Icons.stop,
                      color: Colors.white,
                      size: 28,
                    ),
                    splashRadius: 24,
                  ),
                  
                  const SizedBox(width: 20),
                  
                  // زر التشغيل/الإيقاف المؤقت
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: IconButton(
                      onPressed: audioProvider.isPlaying
                          ? audioProvider.pause
                          : audioProvider.resume,
                      icon: Icon(
                        audioProvider.isPlaying
                            ? Icons.pause
                            : Icons.play_arrow,
                        color: Colors.green.shade700,
                        size: 32,
                      ),
                      splashRadius: 28,
                    ),
                  ),
                  
                  const SizedBox(width: 20),
                  
                  // مؤشر التحميل أو أيقونة فارغة
                  SizedBox(
                    width: 28,
                    height: 28,
                    child: audioProvider.isLoading
                        ? const CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          )
                        : null,
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
