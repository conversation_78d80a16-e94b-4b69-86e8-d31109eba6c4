import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/favorites_provider.dart';
import '../widgets/reciter_selector.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'الإعدادات',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.green.shade700,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // قسم القراء
            _buildSectionHeader('القراء'),
            const SizedBox(height: 8),
            _buildReciterSection(),
            
            const SizedBox(height: 24),
            
            // قسم المفضلات
            _buildSectionHeader('المفضلات'),
            const SizedBox(height: 8),
            _buildFavoritesSection(context),
            
            const SizedBox(height: 24),
            
            // قسم حول التطبيق
            _buildSectionHeader('حول التطبيق'),
            const SizedBox(height: 8),
            _buildAboutSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Colors.green,
      ),
    );
  }

  Widget _buildReciterSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختيار القارئ',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'اختر القارئ المفضل لديك لتلاوة القرآن الكريم',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            const ReciterSelector(),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoritesSection(BuildContext context) {
    return Consumer<FavoritesProvider>(
      builder: (context, favoritesProvider, child) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.favorite, color: Colors.red),
                title: const Text('إحصائيات المفضلات'),
                subtitle: Text(
                  'السور المفضلة: ${favoritesProvider.favoriteSurahs.length}\n'
                  'الآيات المفضلة: ${favoritesProvider.favoriteVerses.length}',
                ),
                trailing: const Icon(Icons.arrow_forward_ios),
              ),
              const Divider(height: 1),
              ListTile(
                leading: const Icon(Icons.refresh, color: Colors.blue),
                title: const Text('تحديث المفضلات'),
                subtitle: const Text('إعادة تحميل قائمة المفضلات'),
                onTap: () {
                  favoritesProvider.refresh();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تحديث المفضلات'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
              ),
              const Divider(height: 1),
              ListTile(
                leading: const Icon(Icons.clear_all, color: Colors.red),
                title: const Text('مسح جميع المفضلات'),
                subtitle: const Text('حذف جميع السور والآيات المفضلة'),
                onTap: () => _showClearFavoritesDialog(context, favoritesProvider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.info, color: Colors.blue),
            title: const Text('معلومات التطبيق'),
            subtitle: const Text('الإصدار 1.0.0'),
            onTap: () => _showAboutDialog(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.api, color: Colors.orange),
            title: const Text('مصدر البيانات'),
            subtitle: const Text('Quran.com API'),
            onTap: () => _showDataSourceDialog(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.code, color: Colors.purple),
            title: const Text('تطوير'),
            subtitle: const Text('تم تطويره باستخدام Flutter'),
          ),
        ],
      ),
    );
  }

  void _showClearFavoritesDialog(BuildContext context, FavoritesProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع المفضلات'),
        content: const Text(
          'هل أنت متأكد من أنك تريد مسح جميع السور والآيات المفضلة؟\n'
          'لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              provider.clearAllFavorites();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم مسح جميع المفضلات'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حول التطبيق'),
        content: const Text(
          'تطبيق القرآن الكريم\n'
          'الإصدار 1.0.0\n\n'
          'تطبيق بسيط وجميل لقراءة والاستماع للقرآن الكريم\n'
          'يحتوي على جميع السور والآيات مع إمكانية الاستماع لتلاوة 5 قراء مختلفين\n\n'
          'المميزات:\n'
          '• عرض جميع سور القرآن الكريم\n'
          '• الاستماع للتلاوة مع 5 قراء\n'
          '• إضافة السور والآيات للمفضلات\n'
          '• البحث في السور والآيات\n'
          '• دعم كامل للغة العربية',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showDataSourceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مصدر البيانات'),
        content: const Text(
          'يستخدم هذا التطبيق البيانات من:\n\n'
          '• Quran.com API للنصوص\n'
          '• MP3Quran.net للتلاوات الصوتية\n\n'
          'جميع البيانات مجانية ومتاحة للاستخدام العام\n'
          'نشكر القائمين على هذه المواقع لتوفير هذه الخدمة المباركة',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
