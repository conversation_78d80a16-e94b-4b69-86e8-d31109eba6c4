import 'package:flutter/foundation.dart';
import 'package:audioplayers/audioplayers.dart';
import '../models/reciter_model.dart';
import '../services/audio_service.dart';
import '../services/favorites_service.dart';

class AudioProvider with ChangeNotifier {
  List<Reciter> _reciters = [];
  Reciter? _selectedReciter;
  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  int? _currentSurahNumber;
  int? _currentVerseNumber;

  // Getters
  List<Reciter> get reciters => _reciters;
  Reciter? get selectedReciter => _selectedReciter;
  bool get isPlaying => _isPlaying;
  bool get isLoading => _isLoading;
  Duration get currentPosition => _currentPosition;
  Duration get totalDuration => _totalDuration;
  int? get currentSurahNumber => _currentSurahNumber;
  int? get currentVerseNumber => _currentVerseNumber;

  AudioProvider() {
    _initializeProvider();
  }

  // تهيئة المزود
  Future<void> _initializeProvider() async {
    _reciters = Reciter.getDefaultReciters();
    await _loadSelectedReciter();
    _setupAudioListeners();
    notifyListeners();
  }

  // تحميل القارئ المحفوظ
  Future<void> _loadSelectedReciter() async {
    try {
      final reciterIdentifier = await FavoritesService.getSelectedReciter();
      _selectedReciter = _reciters.firstWhere(
        (reciter) => reciter.identifier == reciterIdentifier,
        orElse: () => _reciters.first,
      );
    } catch (e) {
      _selectedReciter = _reciters.first;
    }
  }

  // إعداد مستمعي الصوت
  void _setupAudioListeners() {
    // مستمع حالة التشغيل
    AudioService.playerStateStream.listen((PlayerState state) {
      _isPlaying = state == PlayerState.playing;
      notifyListeners();
    });

    // مستمع موضع التشغيل
    AudioService.positionStream.listen((Duration position) {
      _currentPosition = position;
      notifyListeners();
    });

    // مستمع مدة التشغيل
    AudioService.durationStream.listen((Duration? duration) {
      _totalDuration = duration ?? Duration.zero;
      notifyListeners();
    });
  }

  // تشغيل آية
  Future<void> playVerse(int surahNumber, int verseNumber) async {
    if (_selectedReciter == null) return;

    _setLoading(true);
    try {
      await AudioService.playVerse(
        surahNumber: surahNumber,
        verseNumber: verseNumber,
        reciter: _selectedReciter!,
      );
      _currentSurahNumber = surahNumber;
      _currentVerseNumber = verseNumber;
    } catch (e) {
      debugPrint('خطأ في تشغيل الآية: $e');
    } finally {
      _setLoading(false);
    }
  }

  // تشغيل السورة كاملة
  Future<void> playSurah(int surahNumber) async {
    if (_selectedReciter == null) return;

    _setLoading(true);
    try {
      await AudioService.playSurah(
        surahNumber: surahNumber,
        reciter: _selectedReciter!,
      );
      _currentSurahNumber = surahNumber;
      _currentVerseNumber = null;
    } catch (e) {
      debugPrint('خطأ في تشغيل السورة: $e');
    } finally {
      _setLoading(false);
    }
  }

  // إيقاف التشغيل
  Future<void> stop() async {
    await AudioService.stop();
    _currentSurahNumber = null;
    _currentVerseNumber = null;
    _currentPosition = Duration.zero;
    _totalDuration = Duration.zero;
    notifyListeners();
  }

  // إيقاف مؤقت
  Future<void> pause() async {
    await AudioService.pause();
  }

  // استئناف التشغيل
  Future<void> resume() async {
    await AudioService.resume();
  }

  // تغيير القارئ
  Future<void> selectReciter(Reciter reciter) async {
    _selectedReciter = reciter;
    await FavoritesService.saveSelectedReciter(reciter.identifier);

    // إيقاف التشغيل الحالي عند تغيير القارئ
    await stop();

    notifyListeners();
  }

  // الحصول على القارئ بالمعرف
  Reciter? getReciterByIdentifier(String identifier) {
    try {
      return _reciters.firstWhere((reciter) => reciter.identifier == identifier);
    } catch (e) {
      return null;
    }
  }

  // التحقق من كون الآية قيد التشغيل
  bool isVerseCurrentlyPlaying(int surahNumber, int verseNumber) {
    return _isPlaying &&
           _currentSurahNumber == surahNumber &&
           _currentVerseNumber == verseNumber;
  }

  // التحقق من كون السورة قيد التشغيل
  bool isSurahCurrentlyPlaying(int surahNumber) {
    return _isPlaying &&
           _currentSurahNumber == surahNumber &&
           _currentVerseNumber == null;
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  @override
  void dispose() {
    AudioService.dispose();
    super.dispose();
  }
}
