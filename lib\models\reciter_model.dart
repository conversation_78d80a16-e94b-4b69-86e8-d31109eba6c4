class Reciter {
  final String identifier;
  final String name;
  final String englishName;
  final String language;

  Reciter({
    required this.identifier,
    required this.name,
    required this.englishName,
    required this.language,
  });

  factory Reciter.fromJson(Map<String, dynamic> json) {
    return Reciter(
      identifier: json['identifier'] ?? '',
      name: json['name'] ?? '',
      englishName: json['englishName'] ?? '',
      language: json['language'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'identifier': identifier,
      'name': name,
      'englishName': englishName,
      'language': language,
    };
  }

  // القراء المتاحون
  static List<Reciter> getDefaultReciters() {
    return [
      Reciter(
        identifier: 'ar.abdulsamad',
        name: 'عبدالباسط عبدالصمد',
        englishName: '<PERSON>',
        language: 'ar',
      ),
      Reciter(
        identifier: 'ar.minshawi',
        name: 'محمد صديق المنشاوي',
        englishName: '<PERSON>shaw<PERSON>',
        language: 'ar',
      ),
      Reciter(
        identifier: 'ar.ahmedajamy',
        name: 'أحمد بن علي العجمي',
        englishName: 'Ahmed ibn Ali al-Ajamy',
        language: 'ar',
      ),
      Reciter(
        identifier: 'ar.shaatree',
        name: 'أبو بكر الشاطري',
        englishName: 'Abu Bakr Ash-Shaatree',
        language: 'ar',
      ),
      Reciter(
        identifier: 'ar.mahermuaiqly',
        name: 'ماهر المعيقلي',
        englishName: 'Maher Al Muaiqly',
        language: 'ar',
      ),
    ];
  }
}
