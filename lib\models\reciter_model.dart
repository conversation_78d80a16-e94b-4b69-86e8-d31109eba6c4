class Reciter {
  final int id;
  final String name;
  final String arabicName;
  final String baseUrl;

  Reciter({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.baseUrl,
  });

  factory Reciter.fromJson(Map<String, dynamic> json) {
    return Reciter(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      arabicName: json['arabicName'] ?? '',
      baseUrl: json['baseUrl'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'arabicName': arabicName,
      'baseUrl': baseUrl,
    };
  }

  // القراء المتاحون
  static List<Reciter> getDefaultReciters() {
    return [
      Reciter(
        id: 1,
        name: '<PERSON>',
        arabicName: 'عبد الباسط عبد الصمد',
        baseUrl: 'https://server8.mp3quran.net/abd_basit/Alafasy_128_kbps',
      ),
      Reciter(
        id: 2,
        name: '<PERSON>',
        arabicName: 'محمد صدي<PERSON>',
        baseUrl: 'https://server10.mp3quran.net/minsh/Alafasy_128_kbps',
      ),
      Reciter(
        id: 3,
        name: 'Ahmad Ibn Ali Al-Ajmi',
        arabicName: 'أحمد بن علي العجمي',
        baseUrl: 'https://server11.mp3quran.net/ajm/128',
      ),
      Reciter(
        id: 4,
        name: 'Abu Bakr Al-Shatri',
        arabicName: 'أبو بكر الشاطري',
        baseUrl: 'https://server13.mp3quran.net/shatri/128',
      ),
      Reciter(
        id: 5,
        name: 'Maher Al-Muaiqly',
        arabicName: 'ماهر المعيقلي',
        baseUrl: 'https://server12.mp3quran.net/maher/128',
      ),
    ];
  }
}
