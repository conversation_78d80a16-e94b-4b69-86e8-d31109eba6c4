# تطبيق القرآن الكريم 📖

تطبيق Flutter شامل لقراءة والاستماع للقرآن الكريم مع دعم كامل للغة العربية واتجاه النص من اليمين إلى اليسار (RTL).

## المميزات ✨

### 📚 عرض القرآن الكريم
- عرض جميع سور القرآن الكريم (114 سورة)
- عرض آيات كل سورة مع التنسيق الجميل
- معلومات تفصيلية عن كل سورة (عدد الآيات، مكية/مدنية)
- معلومات إضافية عن كل آية (الجزء، الصفحة، السجدة)

### 🎵 تشغيل الصوت
- الاستماع لتلاوة القرآن الكريم عبر الإنترنت (Streaming)
- دعم 5 قراء مختلفين:
  - عبد الباسط عبد الصمد
  - محمد صديق المنشاوي
  - أحمد بن علي العجمي
  - أبو بكر الشاطري
  - ماهر المعيقلي
- تشغيل آية واحدة أو السورة كاملة
- مشغل صوت متقدم مع أزرار التحكم

### ⭐ المفضلات
- إضافة السور والآيات للمفضلات
- عرض المفضلات في تبويبات منفصلة
- البحث في المفضلات
- إدارة المفضلات (حذف، مسح الكل)

### 🔍 البحث
- البحث في السور بالاسم العربي أو الإنجليزي
- البحث في الآيات بالنص العربي
- البحث في المفضلات

### ⚙️ الإعدادات
- اختيار القارئ المفضل
- إدارة المفضلات
- معلومات التطبيق

## التقنيات المستخدمة 🛠️

- **Flutter**: إطار العمل الأساسي
- **Provider**: إدارة الحالة
- **HTTP**: التواصل مع APIs
- **AudioPlayers**: تشغيل الصوت
- **SharedPreferences**: حفظ البيانات محلياً

## مصادر البيانات 📡

- **النصوص**: [Quran.com API](https://api.quran.com)
- **الصوتيات**: [MP3Quran.net](https://mp3quran.net)

## بنية المشروع 📁

```
lib/
├── models/          # نماذج البيانات
├── services/        # خدمات API والصوت والمفضلات
├── providers/       # مقدمي الحالة
├── screens/         # شاشات التطبيق
├── widgets/         # المكونات القابلة لإعادة الاستخدام
└── main.dart        # نقطة البداية
```

## كيفية التشغيل 🚀

1. تأكد من تثبيت Flutter على جهازك
2. استنسخ المشروع:
   ```bash
   git clone [repository-url]
   cd firstapp
   ```
3. ثبت المكتبات:
   ```bash
   flutter pub get
   ```
4. شغل التطبيق:
   ```bash
   flutter run
   ```

## المكتبات المستخدمة 📦

```yaml
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  http: ^1.4.0
  audioplayers: ^6.4.0
  shared_preferences: ^2.5.3
  provider: ^6.1.5
```

## المميزات التقنية 🔧

- **دعم RTL**: دعم كامل للغة العربية واتجاه النص
- **تصميم متجاوب**: يعمل على جميع أحجام الشاشات
- **إدارة الحالة**: استخدام Provider لإدارة فعالة للحالة
- **تخزين محلي**: حفظ المفضلات والإعدادات محلياً
- **تشغيل صوت متقدم**: مع أزرار التحكم وشريط التقدم
- **معالجة الأخطاء**: معالجة شاملة للأخطاء مع رسائل واضحة

## لقطات الشاشة 📱

التطبيق يحتوي على:
- شاشة رئيسية تعرض فهرس السور
- شاشة عرض آيات السورة مع مشغل الصوت
- شاشة المفضلات مع تبويبات
- شاشة الإعدادات لاختيار القارئ

## المساهمة 🤝

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. عمل commit للتغييرات
4. إرسال Pull Request

## الترخيص 📄

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتعليمي.

## الشكر والتقدير 🙏

- شكر خاص لـ Quran.com لتوفير API النصوص
- شكر لـ MP3Quran.net لتوفير التلاوات الصوتية
- شكر لمجتمع Flutter للدعم والموارد

---

**تم تطويره بـ ❤️ باستخدام Flutter**
