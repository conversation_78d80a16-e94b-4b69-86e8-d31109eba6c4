import 'package:flutter/foundation.dart';
import 'package:audioplayers/audioplayers.dart';
import '../models/reciter_model.dart';

class AudioService {
  static final AudioPlayer _audioPlayer = AudioPlayer();
  static bool _isPlaying = false;
  static String? _currentUrl;

  static AudioPlayer get audioPlayer => _audioPlayer;
  static bool get isPlaying => _isPlaying;
  static String? get currentUrl => _currentUrl;

  // تشغيل آية معينة
  static Future<void> playVerse({
    required int surahNumber,
    required int verseNumber,
    required Reciter reciter,
  }) async {
    try {
      // إيقاف التشغيل الحالي إن وجد
      await stop();

      // تكوين رابط الصوت
      String audioUrl = _buildAudioUrl(surahNumber, verseNumber, reciter);
      
      // تشغيل الصوت
      await _audioPlayer.play(UrlSource(audioUrl));
      _isPlaying = true;
      _currentUrl = audioUrl;
      
    } catch (e) {
      throw Exception('فشل في تشغيل الآية: $e');
    }
  }

  // تشغيل السورة كاملة
  static Future<void> playSurah({
    required int surahNumber,
    required Reciter reciter,
  }) async {
    try {
      // إيقاف التشغيل الحالي إن وجد
      await stop();

      // تكوين رابط الصوت للسورة كاملة
      String audioUrl = _buildSurahAudioUrl(surahNumber, reciter);
      
      // تشغيل الصوت
      await _audioPlayer.play(UrlSource(audioUrl));
      _isPlaying = true;
      _currentUrl = audioUrl;
      
    } catch (e) {
      throw Exception('فشل في تشغيل السورة: $e');
    }
  }

  // إيقاف التشغيل
  static Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _isPlaying = false;
      _currentUrl = null;
    } catch (e) {
      debugPrint('خطأ في إيقاف التشغيل: $e');
    }
  }

  // إيقاف مؤقت
  static Future<void> pause() async {
    try {
      await _audioPlayer.pause();
      _isPlaying = false;
    } catch (e) {
      debugPrint('خطأ في الإيقاف المؤقت: $e');
    }
  }

  // استئناف التشغيل
  static Future<void> resume() async {
    try {
      await _audioPlayer.resume();
      _isPlaying = true;
    } catch (e) {
      debugPrint('خطأ في استئناف التشغيل: $e');
    }
  }

  // بناء رابط الصوت للآية
  static String _buildAudioUrl(int surahNumber, int verseNumber, Reciter reciter) {
    // تنسيق أرقام السورة والآية بثلاث خانات
    String surahStr = surahNumber.toString().padLeft(3, '0');
    String verseStr = verseNumber.toString().padLeft(3, '0');
    
    return '${reciter.baseUrl}/$surahStr$verseStr.mp3';
  }

  // بناء رابط الصوت للسورة كاملة
  static String _buildSurahAudioUrl(int surahNumber, Reciter reciter) {
    String surahStr = surahNumber.toString().padLeft(3, '0');
    return '${reciter.baseUrl}/$surahStr.mp3';
  }

  // الحصول على حالة التشغيل
  static Stream<PlayerState> get playerStateStream => _audioPlayer.onPlayerStateChanged;

  // الحصول على موضع التشغيل
  static Stream<Duration> get positionStream => _audioPlayer.onPositionChanged;

  // الحصول على مدة التشغيل
  static Stream<Duration?> get durationStream => _audioPlayer.onDurationChanged;

  // تنظيف الموارد
  static Future<void> dispose() async {
    await _audioPlayer.dispose();
  }
}
