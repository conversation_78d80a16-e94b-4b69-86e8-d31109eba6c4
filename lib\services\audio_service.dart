import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:http/http.dart' as http;
import '../models/reciter_model.dart';

class AudioService {
  static final AudioPlayer _audioPlayer = AudioPlayer();
  static bool _isPlaying = false;
  static String? _currentUrl;

  static AudioPlayer get audioPlayer => _audioPlayer;
  static bool get isPlaying => _isPlaying;
  static String? get currentUrl => _currentUrl;

  // تشغيل آية معينة
  static Future<void> playVerse({
    required int surahNumber,
    required int verseNumber,
    required Reciter reciter,
  }) async {
    try {
      // إيقاف التشغيل الحالي إن وجد
      await stop();

      // تكوين رابط الصوت باستخدام API
      String audioUrl = 'https://api.alquran.cloud/v1/ayah/$surahNumber:$verseNumber/${reciter.identifier}';
      debugPrint('طلب الصوت: $audioUrl');

      // جلب بيانات الآية مع الصوت
      final response = await http.get(Uri.parse(audioUrl));
      debugPrint('رد الخادم: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final String? audioFileUrl = data['data']['audio'];
        debugPrint('رابط الملف الصوتي: $audioFileUrl');

        if (audioFileUrl != null && audioFileUrl.isNotEmpty) {
          await _audioPlayer.play(UrlSource(audioFileUrl));
          _isPlaying = true;
          _currentUrl = audioFileUrl;
          debugPrint('تم بدء التشغيل بنجاح');
        } else {
          throw Exception('لا يوجد ملف صوتي لهذه الآية');
        }
      } else {
        debugPrint('خطأ في الاستجابة: ${response.body}');
        throw Exception('فشل في جلب بيانات الآية: ${response.statusCode}');
      }

    } catch (e) {
      throw Exception('فشل في تشغيل الآية: $e');
    }
  }

  // تشغيل السورة كاملة
  static Future<void> playSurah({
    required int surahNumber,
    required Reciter reciter,
  }) async {
    try {
      // إيقاف التشغيل الحالي إن وجد
      await stop();

      // تكوين رابط الصوت للسورة كاملة باستخدام API
      String audioUrl = 'https://api.alquran.cloud/v1/surah/$surahNumber/${reciter.identifier}';

      // جلب بيانات السورة مع الصوت
      final response = await http.get(Uri.parse(audioUrl));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> ayahs = data['data']['ayahs'];

        if (ayahs.isNotEmpty) {
          final String? audioFileUrl = ayahs[0]['audio'];
          if (audioFileUrl != null && audioFileUrl.isNotEmpty) {
            await _audioPlayer.play(UrlSource(audioFileUrl));
            _isPlaying = true;
            _currentUrl = audioFileUrl;
          } else {
            throw Exception('لا يوجد ملف صوتي لهذه السورة');
          }
        } else {
          throw Exception('لا توجد آيات في هذه السورة');
        }
      } else {
        throw Exception('فشل في جلب بيانات السورة');
      }

    } catch (e) {
      throw Exception('فشل في تشغيل السورة: $e');
    }
  }

  // إيقاف التشغيل
  static Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _isPlaying = false;
      _currentUrl = null;
    } catch (e) {
      debugPrint('خطأ في إيقاف التشغيل: $e');
    }
  }

  // إيقاف مؤقت
  static Future<void> pause() async {
    try {
      await _audioPlayer.pause();
      _isPlaying = false;
    } catch (e) {
      debugPrint('خطأ في الإيقاف المؤقت: $e');
    }
  }

  // استئناف التشغيل
  static Future<void> resume() async {
    try {
      await _audioPlayer.resume();
      _isPlaying = true;
    } catch (e) {
      debugPrint('خطأ في استئناف التشغيل: $e');
    }
  }



  // الحصول على حالة التشغيل
  static Stream<PlayerState> get playerStateStream => _audioPlayer.onPlayerStateChanged;

  // الحصول على موضع التشغيل
  static Stream<Duration> get positionStream => _audioPlayer.onPositionChanged;

  // الحصول على مدة التشغيل
  static Stream<Duration?> get durationStream => _audioPlayer.onDurationChanged;

  // تنظيف الموارد
  static Future<void> dispose() async {
    await _audioPlayer.dispose();
  }
}
