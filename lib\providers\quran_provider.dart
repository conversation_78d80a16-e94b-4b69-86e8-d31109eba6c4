import 'package:flutter/foundation.dart';
import '../models/surah_model.dart';
import '../models/verse_model.dart';
import '../services/quran_api_service.dart';

class QuranProvider with ChangeNotifier {
  List<Surah> _surahs = [];
  List<Verse> _currentVerses = [];
  Surah? _currentSurah;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Surah> get surahs => _surahs;
  List<Verse> get currentVerses => _currentVerses;
  Surah? get currentSurah => _currentSurah;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // جلب قائمة السور
  Future<void> loadSurahs() async {
    _setLoading(true);
    _setError(null);

    try {
      _surahs = await QuranApiService.getSurahs();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // جلب آيات سورة معينة
  Future<void> loadSurahVerses(int surahNumber) async {
    _setLoading(true);
    _setError(null);

    try {
      final result = await QuranApiService.getSurahWithVerses(surahNumber);
      _currentSurah = result['surah'];
      _currentVerses = result['verses'];
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // البحث في السور
  List<Surah> searchSurahs(String query) {
    if (query.isEmpty) return _surahs;
    
    return _surahs.where((surah) {
      return surah.name.contains(query) ||
             surah.englishName.toLowerCase().contains(query.toLowerCase()) ||
             surah.englishNameTranslation.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // البحث في الآيات
  List<Verse> searchVerses(String query) {
    if (query.isEmpty) return _currentVerses;
    
    return _currentVerses.where((verse) {
      return verse.text.contains(query);
    }).toList();
  }

  // الحصول على سورة بالرقم
  Surah? getSurahByNumber(int number) {
    try {
      return _surahs.firstWhere((surah) => surah.number == number);
    } catch (e) {
      return null;
    }
  }

  // تنظيف البيانات الحالية
  void clearCurrentData() {
    _currentSurah = null;
    _currentVerses = [];
    notifyListeners();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // إعادة تحميل البيانات
  Future<void> refresh() async {
    await loadSurahs();
  }
}
