import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/surah_model.dart';
import '../models/verse_model.dart';

class FavoritesService {
  static const String _favoriteSurahsKey = 'favorite_surahs';
  static const String _favoriteVersesKey = 'favorite_verses';

  // إضافة سورة للمفضلات
  static Future<void> addFavoriteSurah(Surah surah) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> favoriteSurahs = prefs.getStringList(_favoriteSurahsKey) ?? [];
      
      String surahJson = json.encode(surah.toJson());
      if (!favoriteSurahs.contains(surahJson)) {
        favoriteSurahs.add(surahJson);
        await prefs.setStringList(_favoriteSurahsKey, favoriteSurahs);
      }
    } catch (e) {
      throw Exception('فشل في إضافة السورة للمفضلات: $e');
    }
  }

  // إزالة سورة من المفضلات
  static Future<void> removeFavoriteSurah(Surah surah) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> favoriteSurahs = prefs.getStringList(_favoriteSurahsKey) ?? [];
      
      String surahJson = json.encode(surah.toJson());
      favoriteSurahs.remove(surahJson);
      await prefs.setStringList(_favoriteSurahsKey, favoriteSurahs);
    } catch (e) {
      throw Exception('فشل في إزالة السورة من المفضلات: $e');
    }
  }

  // جلب السور المفضلة
  static Future<List<Surah>> getFavoriteSurahs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> favoriteSurahs = prefs.getStringList(_favoriteSurahsKey) ?? [];
      
      return favoriteSurahs
          .map((surahString) => Surah.fromJson(json.decode(surahString)))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب السور المفضلة: $e');
    }
  }

  // التحقق من كون السورة مفضلة
  static Future<bool> isFavoriteSurah(Surah surah) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> favoriteSurahs = prefs.getStringList(_favoriteSurahsKey) ?? [];
      
      String surahJson = json.encode(surah.toJson());
      return favoriteSurahs.contains(surahJson);
    } catch (e) {
      return false;
    }
  }

  // إضافة آية للمفضلات
  static Future<void> addFavoriteVerse(Verse verse) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> favoriteVerses = prefs.getStringList(_favoriteVersesKey) ?? [];
      
      String verseJson = json.encode(verse.toJson());
      if (!favoriteVerses.contains(verseJson)) {
        favoriteVerses.add(verseJson);
        await prefs.setStringList(_favoriteVersesKey, favoriteVerses);
      }
    } catch (e) {
      throw Exception('فشل في إضافة الآية للمفضلات: $e');
    }
  }

  // إزالة آية من المفضلات
  static Future<void> removeFavoriteVerse(Verse verse) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> favoriteVerses = prefs.getStringList(_favoriteVersesKey) ?? [];
      
      String verseJson = json.encode(verse.toJson());
      favoriteVerses.remove(verseJson);
      await prefs.setStringList(_favoriteVersesKey, favoriteVerses);
    } catch (e) {
      throw Exception('فشل في إزالة الآية من المفضلات: $e');
    }
  }

  // جلب الآيات المفضلة
  static Future<List<Verse>> getFavoriteVerses() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> favoriteVerses = prefs.getStringList(_favoriteVersesKey) ?? [];
      
      return favoriteVerses
          .map((verseString) => Verse.fromJson(json.decode(verseString)))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الآيات المفضلة: $e');
    }
  }

  // التحقق من كون الآية مفضلة
  static Future<bool> isFavoriteVerse(Verse verse) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> favoriteVerses = prefs.getStringList(_favoriteVersesKey) ?? [];
      
      String verseJson = json.encode(verse.toJson());
      return favoriteVerses.contains(verseJson);
    } catch (e) {
      return false;
    }
  }

  // حفظ القارئ المفضل
  static Future<void> saveSelectedReciter(String reciterIdentifier) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selected_reciter', reciterIdentifier);
    } catch (e) {
      throw Exception('فشل في حفظ القارئ المفضل: $e');
    }
  }

  // جلب القارئ المفضل
  static Future<String> getSelectedReciter() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('selected_reciter') ?? 'ar.abdulsamad'; // القارئ الافتراضي
    } catch (e) {
      return 'ar.abdulsamad';
    }
  }
}
