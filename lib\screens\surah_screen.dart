import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/surah_model.dart';
import '../providers/quran_provider.dart';
import '../providers/audio_provider.dart';
import '../providers/favorites_provider.dart';
import '../widgets/verse_card.dart';
import '../widgets/audio_player_widget.dart';

class SurahScreen extends StatefulWidget {
  final Surah surah;

  const SurahScreen({super.key, required this.surah});

  @override
  State<SurahScreen> createState() => _SurahScreenState();
}

class _SurahScreenState extends State<SurahScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<QuranProvider>().loadSurahVerses(widget.surah.number);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            widget.surah.name,
            style: const TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: Colors.green.shade700,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
          actions: [
            Consumer<FavoritesProvider>(
              builder: (context, favoritesProvider, child) {
                final isFavorite = favoritesProvider.isFavoriteSurah(widget.surah);
                return IconButton(
                  icon: Icon(
                    isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    favoritesProvider.toggleFavoriteSurah(widget.surah);
                  },
                );
              },
            ),
            Consumer<AudioProvider>(
              builder: (context, audioProvider, child) {
                final isPlaying = audioProvider.isSurahCurrentlyPlaying(widget.surah.number);
                return IconButton(
                  icon: Icon(
                    isPlaying ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    if (isPlaying) {
                      audioProvider.pause();
                    } else {
                      audioProvider.playSurah(widget.surah.number);
                    }
                  },
                );
              },
            ),
          ],
        ),
        body: Column(
          children: [
            // معلومات السورة
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.green.shade700,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  Text(
                    widget.surah.englishNameTranslation,
                    style: const TextStyle(
                      fontSize: 18,
                      color: Colors.white70,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${widget.surah.numberOfAyahs} آية • ${widget.surah.revelationType == 'Meccan' ? 'مكية' : 'مدنية'}',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.white60,
                    ),
                  ),
                ],
              ),
            ),

            // مشغل الصوت
            const AudioPlayerWidget(),

            // قائمة الآيات
            Expanded(
              child: Consumer<QuranProvider>(
                builder: (context, quranProvider, child) {
                  if (quranProvider.isLoading) {
                    return const Center(
                      child: CircularProgressIndicator(
                        color: Colors.green,
                      ),
                    );
                  }

                  if (quranProvider.error != null) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: Colors.red.shade300,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'حدث خطأ في تحميل الآيات',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              quranProvider.loadSurahVerses(widget.surah.number);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('إعادة المحاولة'),
                          ),
                        ],
                      ),
                    );
                  }

                  final verses = quranProvider.currentVerses;

                  if (verses.isEmpty) {
                    return const Center(
                      child: Text(
                        'لا توجد آيات',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey,
                        ),
                      ),
                    );
                  }

                  return ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    itemCount: verses.length,
                    itemBuilder: (context, index) {
                      final verse = verses[index];
                      return Consumer2<AudioProvider, FavoritesProvider>(
                        builder: (context, audioProvider, favoritesProvider, child) {
                          return VerseCard(
                            verse: verse,
                            surahName: widget.surah.name,
                            isPlaying: audioProvider.isVerseCurrentlyPlaying(
                              verse.surahNumber,
                              verse.number,
                            ),
                            isFavorite: favoritesProvider.isFavoriteVerse(verse),
                            onPlayTap: () {
                              if (audioProvider.isVerseCurrentlyPlaying(
                                verse.surahNumber,
                                verse.number,
                              )) {
                                audioProvider.pause();
                              } else {
                                audioProvider.playVerse(
                                  verse.surahNumber,
                                  verse.number,
                                );
                              }
                            },
                            onFavoriteTap: () {
                              favoritesProvider.toggleFavoriteVerse(verse);
                            },
                          );
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
