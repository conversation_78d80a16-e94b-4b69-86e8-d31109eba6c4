import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/surah_model.dart';
import '../models/verse_model.dart';

class QuranApiService {
  static const String baseUrl = 'http://api.alquran.cloud/v1';

  // جلب قائمة السور
  static Future<List<Surah>> getSurahs() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/surah'));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> surahsData = data['data'];
        
        return surahsData.map((surahJson) => Surah.fromJson(surahJson)).toList();
      } else {
        throw Exception('فشل في جلب السور');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  // جلب آيات سورة معينة
  static Future<List<Verse>> getVerses(int surahNumber) async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/surah/$surahNumber'));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> versesData = data['data']['ayahs'];
        
        return versesData.map((verseJson) => Verse.fromJson(verseJson)).toList();
      } else {
        throw Exception('فشل في جلب آيات السورة');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }

  // جلب سورة واحدة مع آياتها
  static Future<Map<String, dynamic>> getSurahWithVerses(int surahNumber) async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/surah/$surahNumber'));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final surahData = data['data'];
        
        final surah = Surah(
          number: surahData['number'],
          name: surahData['name'],
          englishName: surahData['englishName'],
          englishNameTranslation: surahData['englishNameTranslation'],
          numberOfAyahs: surahData['numberOfAyahs'],
          revelationType: surahData['revelationType'],
        );
        
        final List<dynamic> versesData = surahData['ayahs'];
        final verses = versesData.map((verseJson) => Verse.fromJson(verseJson)).toList();
        
        return {
          'surah': surah,
          'verses': verses,
        };
      } else {
        throw Exception('فشل في جلب السورة');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: $e');
    }
  }
}
