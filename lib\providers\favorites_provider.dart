import 'package:flutter/foundation.dart';
import '../models/surah_model.dart';
import '../models/verse_model.dart';
import '../services/favorites_service.dart';

class FavoritesProvider with ChangeNotifier {
  List<Surah> _favoriteSurahs = [];
  List<Verse> _favoriteVerses = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Surah> get favoriteSurahs => _favoriteSurahs;
  List<Verse> get favoriteVerses => _favoriteVerses;
  bool get isLoading => _isLoading;
  String? get error => _error;

  FavoritesProvider() {
    loadFavorites();
  }

  // تحميل المفضلات
  Future<void> loadFavorites() async {
    _setLoading(true);
    _setError(null);

    try {
      _favoriteSurahs = await FavoritesService.getFavoriteSurahs();
      _favoriteVerses = await FavoritesService.getFavoriteVerses();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // إضافة سورة للمفضلات
  Future<void> addFavoriteSurah(Surah surah) async {
    try {
      await FavoritesService.addFavoriteSurah(surah);
      if (!_favoriteSurahs.any((s) => s.number == surah.number)) {
        _favoriteSurahs.add(surah);
        notifyListeners();
      }
    } catch (e) {
      _setError(e.toString());
    }
  }

  // إزالة سورة من المفضلات
  Future<void> removeFavoriteSurah(Surah surah) async {
    try {
      await FavoritesService.removeFavoriteSurah(surah);
      _favoriteSurahs.removeWhere((s) => s.number == surah.number);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // تبديل حالة السورة في المفضلات
  Future<void> toggleFavoriteSurah(Surah surah) async {
    if (isFavoriteSurah(surah)) {
      await removeFavoriteSurah(surah);
    } else {
      await addFavoriteSurah(surah);
    }
  }

  // التحقق من كون السورة مفضلة
  bool isFavoriteSurah(Surah surah) {
    return _favoriteSurahs.any((s) => s.number == surah.number);
  }

  // إضافة آية للمفضلات
  Future<void> addFavoriteVerse(Verse verse) async {
    try {
      await FavoritesService.addFavoriteVerse(verse);
      if (!_favoriteVerses.any((v) => 
          v.surahNumber == verse.surahNumber && v.number == verse.number)) {
        _favoriteVerses.add(verse);
        notifyListeners();
      }
    } catch (e) {
      _setError(e.toString());
    }
  }

  // إزالة آية من المفضلات
  Future<void> removeFavoriteVerse(Verse verse) async {
    try {
      await FavoritesService.removeFavoriteVerse(verse);
      _favoriteVerses.removeWhere((v) => 
          v.surahNumber == verse.surahNumber && v.number == verse.number);
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // تبديل حالة الآية في المفضلات
  Future<void> toggleFavoriteVerse(Verse verse) async {
    if (isFavoriteVerse(verse)) {
      await removeFavoriteVerse(verse);
    } else {
      await addFavoriteVerse(verse);
    }
  }

  // التحقق من كون الآية مفضلة
  bool isFavoriteVerse(Verse verse) {
    return _favoriteVerses.any((v) => 
        v.surahNumber == verse.surahNumber && v.number == verse.number);
  }

  // البحث في السور المفضلة
  List<Surah> searchFavoriteSurahs(String query) {
    if (query.isEmpty) return _favoriteSurahs;
    
    return _favoriteSurahs.where((surah) {
      return surah.name.contains(query) ||
             surah.englishName.toLowerCase().contains(query.toLowerCase()) ||
             surah.englishNameTranslation.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // البحث في الآيات المفضلة
  List<Verse> searchFavoriteVerses(String query) {
    if (query.isEmpty) return _favoriteVerses;
    
    return _favoriteVerses.where((verse) {
      return verse.text.contains(query);
    }).toList();
  }

  // الحصول على عدد المفضلات
  int get totalFavoritesCount => _favoriteSurahs.length + _favoriteVerses.length;

  // مسح جميع المفضلات
  Future<void> clearAllFavorites() async {
    try {
      // مسح السور المفضلة
      for (var surah in List.from(_favoriteSurahs)) {
        await FavoritesService.removeFavoriteSurah(surah);
      }
      
      // مسح الآيات المفضلة
      for (var verse in List.from(_favoriteVerses)) {
        await FavoritesService.removeFavoriteVerse(verse);
      }
      
      _favoriteSurahs.clear();
      _favoriteVerses.clear();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // إعادة تحميل المفضلات
  Future<void> refresh() async {
    await loadFavorites();
  }
}
